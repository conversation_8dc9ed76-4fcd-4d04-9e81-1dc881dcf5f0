"""
宿舍数据访问层
"""
from typing import List, Optional
from datetime import date
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from app.models.dormitory import Dormitory
from app.models.record import ResidenceRecord
from .base import BaseRepository


class DormitoryRepository(BaseRepository[Dormitory]):
    """宿舍仓储类"""
    
    def __init__(self, db: Session):
        super().__init__(db, Dormitory)
    
    def get_by_name(self, name: str) -> Optional[Dormitory]:
        """根据名称获取宿舍"""
        return self.db.query(Dormitory).filter(Dormitory.name == name).first()
    
    def get_active_dormitories(self) -> List[Dormitory]:
        """获取所有启用的宿舍"""
        return self.db.query(Dormitory).filter(Dormitory.is_active == True).all()
    
    def get_occupied_beds_count(self, dormitory_id: str, target_date: date = None) -> int:
        """获取指定日期宿舍已占用床位数"""
        if target_date is None:
            target_date = date.today()
        
        return (
            self.db.query(func.count(ResidenceRecord.id))
            .filter(ResidenceRecord.dormitory_id == dormitory_id)
            .filter(ResidenceRecord.status == 'ACTIVE')
            .filter(ResidenceRecord.check_in_date <= target_date)
            .filter(
                or_(
                    ResidenceRecord.check_out_date.is_(None),
                    ResidenceRecord.check_out_date > target_date
                )
            )
            .scalar() or 0
        )
    
    def get_available_beds(self, dormitory_id: str, target_date: date = None) -> List[int]:
        """获取指定日期宿舍可用床位号列表"""
        if target_date is None:
            target_date = date.today()
        
        dormitory = self.get_by_id(dormitory_id)
        if not dormitory:
            return []
        
        # 获取已占用的床位号
        occupied_beds = (
            self.db.query(ResidenceRecord.bed_number)
            .filter(ResidenceRecord.dormitory_id == dormitory_id)
            .filter(ResidenceRecord.status == 'ACTIVE')
            .filter(ResidenceRecord.check_in_date <= target_date)
            .filter(
                or_(
                    ResidenceRecord.check_out_date.is_(None),
                    ResidenceRecord.check_out_date > target_date
                )
            )
            .all()
        )
        
        occupied_bed_numbers = {bed[0] for bed in occupied_beds}
        all_bed_numbers = set(range(1, dormitory.total_beds + 1))
        
        return sorted(list(all_bed_numbers - occupied_bed_numbers))
    
    def check_bed_available(self, dormitory_id: str, bed_number: int, 
                           check_in_date: date, check_out_date: date = None,
                           exclude_record_id: str = None) -> bool:
        """检查床位在指定时间段是否可用"""
        query = (
            self.db.query(ResidenceRecord)
            .filter(ResidenceRecord.dormitory_id == dormitory_id)
            .filter(ResidenceRecord.bed_number == bed_number)
            .filter(ResidenceRecord.status == 'ACTIVE')
        )
        
        if exclude_record_id:
            query = query.filter(ResidenceRecord.id != exclude_record_id)
        
        # 检查时间段冲突
        if check_out_date:
            # 有明确的离开日期
            query = query.filter(
                and_(
                    ResidenceRecord.check_in_date < check_out_date,
                    or_(
                        ResidenceRecord.check_out_date.is_(None),
                        ResidenceRecord.check_out_date > check_in_date
                    )
                )
            )
        else:
            # 没有离开日期（长期入住）
            query = query.filter(
                or_(
                    ResidenceRecord.check_out_date.is_(None),
                    ResidenceRecord.check_out_date > check_in_date
                )
            )
        
        return query.first() is None
    
    def check_name_exists(self, name: str, exclude_id: str = None) -> bool:
        """检查宿舍名称是否已存在"""
        query = self.db.query(Dormitory).filter(Dormitory.name == name)
        if exclude_id:
            query = query.filter(Dormitory.id != exclude_id)
        return query.first() is not None
