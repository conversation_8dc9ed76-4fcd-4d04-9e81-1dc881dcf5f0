"""
报表服务
"""
from typing import List, Optional, Tuple
from datetime import date, datetime
from sqlalchemy.orm import Session

from app.models.dormitory import Dormitory
from app.models.record import ResidenceRecord
from app.models.report import MonthlyReport, DailyAllocation
from app.repositories.report_repo import ReportRepository, DailyAllocationRepository
from app.repositories.dormitory_repo import DormitoryRepository
from app.repositories.record_repo import RecordRepository
from app.services.allocation_calculator import AllocationCalculator
from app.schemas.report import MonthlyReportResponse, DailyAllocationResponse, DailyAllocationDetail
from app.core.logging import get_logger
from app.core.config import settings

logger = get_logger(__name__)


class ReportService:
    """报表服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.report_repo = ReportRepository(db)
        self.daily_repo = DailyAllocationRepository(db)
        self.dormitory_repo = DormitoryRepository(db)
        self.record_repo = RecordRepository(db)
        self.calculator = AllocationCalculator(settings.unit_cost_per_bed_day)
    
    async def get_monthly_report(
        self, 
        year: int, 
        month: int, 
        end_date: Optional[date] = None,
        regenerate: bool = False
    ) -> MonthlyReportResponse:
        """
        获取月度报告
        
        Args:
            year: 年份
            month: 月份
            end_date: 截止日期，用于实时统计
            regenerate: 是否强制重新生成
            
        Returns:
            MonthlyReportResponse: 月度报告
        """
        logger.info(f"开始生成月度报告: {year}年{month}月, 截止日期: {end_date}")
        
        # 检查是否已存在报告且不需要重新生成
        if not regenerate and end_date is None:
            existing_report = self.report_repo.get_by_year_month(year, month)
            if existing_report:
                logger.info(f"使用已存在的月度报告: {existing_report.id}")
                return self._convert_to_response(existing_report)
        
        # 生成新的月度报告
        start_time = datetime.now()
        
        # 1. 获取日期范围
        date_range = self.calculator.get_month_date_range(year, month, end_date)
        
        # 2. 获取所有宿舍和入住记录
        dormitories = self.dormitory_repo.get_active_dormitories()
        residence_records = self.record_repo.get_records_in_date_range(
            date_range[0], date_range[-1]
        )
        
        # 3. 计算每日分摊
        daily_allocations = []
        for target_date in date_range:
            for dormitory in dormitories:
                daily_result = self.calculator.calculate_daily_allocation(
                    target_date, dormitory, residence_records
                )
                daily_allocations.append(daily_result)
        
        # 4. 计算月度汇总
        department_summary = self.calculator.calculate_monthly_summary(
            year, month, daily_allocations, end_date
        )
        
        # 5. 保存报告（仅完整月份）
        if end_date is None:
            report = self.report_repo.create_monthly_report(
                year=year,
                month=month,
                total_bed_days=sum(s.bed_days for s in department_summary),
                department_summary=[s.model_dump() for s in department_summary],
                daily_details=[self._daily_result_to_dict(d) for d in daily_allocations]
            )
            report_id = report.id
        else:
            report_id = None
        
        # 6. 记录计算耗时
        duration = (datetime.now() - start_time).total_seconds()
        logger.info(f"月度报告生成完成，耗时: {duration:.2f}秒")
        
        return MonthlyReportResponse(
            id=report_id or "temp",
            year=year,
            month=month,
            report_date=end_date or date_range[-1],
            total_bed_days=sum(s.bed_days for s in department_summary),
            department_summary=department_summary,
            daily_details=[self._daily_result_to_detail(d) for d in daily_allocations],
            created_at=datetime.now().isoformat(),
            updated_at=datetime.now().isoformat()
        )
    
    async def get_daily_allocations(
        self,
        start_date: date,
        end_date: date,
        dormitory_id: Optional[str] = None
    ) -> List[DailyAllocationResponse]:
        """获取日度分摊明细"""
        try:
            allocations = self.daily_repo.get_by_date_range(start_date, end_date, dormitory_id)
            
            result = []
            for allocation in allocations:
                result.append(DailyAllocationResponse(
                    id=allocation.id,
                    allocation_date=allocation.allocation_date,
                    dormitory_id=allocation.dormitory_id,
                    dormitory_name=allocation.dormitory.name if allocation.dormitory else "",
                    total_beds=allocation.total_beds,
                    occupied_beds=allocation.occupied_beds,
                    department_allocations=allocation.department_allocations
                ))
            
            logger.info(f"获取日度分摊明细成功，共{len(result)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取日度分摊明细失败: {str(e)}")
            raise
    
    async def get_realtime_report(self) -> MonthlyReportResponse:
        """获取当月实时报告（截止到今天）"""
        now = datetime.now()
        return await self.get_monthly_report(now.year, now.month, now.date(), regenerate=True)
    
    def _convert_to_response(self, report: MonthlyReport) -> MonthlyReportResponse:
        """将数据库模型转换为响应模型"""
        from app.schemas.report import DepartmentSummary
        
        # 转换部门汇总
        department_summary = []
        for dept_data in report.department_summary:
            department_summary.append(DepartmentSummary(**dept_data))
        
        # 转换日度明细
        daily_details = []
        for daily_data in report.daily_details:
            daily_details.append(DailyAllocationDetail(**daily_data))
        
        return MonthlyReportResponse(
            id=report.id,
            year=report.year,
            month=report.month,
            report_date=report.report_date,
            total_bed_days=float(report.total_bed_days),
            department_summary=department_summary,
            daily_details=daily_details,
            created_at=report.created_at.isoformat(),
            updated_at=report.updated_at.isoformat()
        )
    
    def _daily_result_to_dict(self, daily_result) -> dict:
        """将日度结果转换为字典"""
        return {
            "date": daily_result.date.isoformat(),
            "dormitory_id": daily_result.dormitory_id,
            "dormitory_name": daily_result.dormitory_name,
            "total_beds": daily_result.total_beds,
            "occupied_beds": daily_result.occupied_beds,
            "department_allocations": [
                {
                    "department_id": alloc.department_id,
                    "department_name": alloc.department_name,
                    "bed_count": alloc.bed_count,
                    "allocation_ratio": alloc.allocation_ratio
                }
                for alloc in daily_result.department_allocations
            ]
        }
    
    def _daily_result_to_detail(self, daily_result) -> DailyAllocationDetail:
        """将日度结果转换为响应模型"""
        return DailyAllocationDetail(
            date=daily_result.date,
            dormitory_id=daily_result.dormitory_id,
            dormitory_name=daily_result.dormitory_name,
            total_beds=daily_result.total_beds,
            occupied_beds=daily_result.occupied_beds,
            department_allocations=[
                {
                    "department_id": alloc.department_id,
                    "department_name": alloc.department_name,
                    "bed_count": alloc.bed_count,
                    "allocation_ratio": alloc.allocation_ratio
                }
                for alloc in daily_result.department_allocations
            ]
        )
