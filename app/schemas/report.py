"""
报表相关模式
"""
from typing import List, Optional, Any, Dict
from datetime import date
from pydantic import BaseModel, Field


class DepartmentSummary(BaseModel):
    """部门汇总信息"""
    department_id: str
    department_name: str
    bed_days: float
    cost: float
    ratio: float


class DailyAllocationDetail(BaseModel):
    """日度分摊明细"""
    date: date
    dormitory_id: str
    dormitory_name: str
    total_beds: int
    occupied_beds: int
    department_allocations: List[Dict[str, Any]] = []


class MonthlyReportResponse(BaseModel):
    """月度报告响应模式"""
    id: str
    year: int
    month: int
    report_date: date
    total_bed_days: float
    department_summary: List[DepartmentSummary] = []
    daily_details: List[DailyAllocationDetail] = []
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class DailyAllocationResponse(BaseModel):
    """日度分摊响应模式"""
    id: str
    allocation_date: date
    dormitory_id: str
    dormitory_name: str
    total_beds: int
    occupied_beds: int
    department_allocations: List[Dict[str, Any]] = []


class ReportExportRequest(BaseModel):
    """报告导出请求"""
    report_type: str
    export_format: str
    year: int
    month: int
    end_date: Optional[date] = None
